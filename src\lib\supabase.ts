import { createClient } from '@supabase/supabase-js'

// Environment variables with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Validate environment variables
const hasValidConfig =
  supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'placeholder-key' &&
  supabaseUrl.includes('supabase.co')

// Enhanced logging for debugging
export function logSupabaseConfig() {
  console.log('📊 Supabase Configuration:')
  console.log('  URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
  console.log('  Anon Key:', supabaseAnonKey ? '✅ Set' : '❌ Missing')
  console.log('  Service Key:', supabaseServiceKey ? '✅ Set' : '❌ Missing')
  console.log('  Environment:', process.env.NODE_ENV)
  console.log('  Vercel Env:', process.env.VERCEL_ENV)
  console.log('  Valid Config:', hasValidConfig)
}

// Client for public operations (using anon key)
export const supabase = hasValidConfig
  ? createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
      },
    })
  : null

// Admin client for server-side operations (using service role key)
export const supabaseAdmin =
  hasValidConfig &&
  supabaseServiceKey &&
  supabaseServiceKey !== 'placeholder-key'
    ? createClient(supabaseUrl!, supabaseServiceKey, {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: false,
        },
      })
    : null

// Helper to check if Supabase is properly configured
export const isSupabaseConfigured = () => hasValidConfig

// Utility function to test connection
export async function testSupabaseConnection() {
  if (!supabase) {
    return { success: false, error: 'Supabase client not initialized' }
  }

  try {
    const { data, error } = await supabase
      .from('bookings')
      .select('count')
      .limit(1)

    if (error) {
      console.error('❌ Supabase connection test failed:', error)
      return { success: false, error: error.message }
    }

    console.log('✅ Supabase connection test successful')
    return { success: true, data }
  } catch (error) {
    console.error('❌ Supabase connection test error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Database Types
export interface Contact {
  id: string
  name: string
  email: string
  message: string
  source: string
  created_at: string
}

export interface Analytics {
  id: string
  page: string
  user_agent?: string
  referrer?: string
  ip_address?: string
  created_at: string
}

export interface Booking {
  id: string
  calendly_id: string
  event_type: string
  event_name: string
  invitee_email: string
  invitee_name: string
  invitee_timezone?: string
  scheduled_at: string
  start_time: string
  end_time: string
  status: 'active' | 'canceled' | 'completed' | 'rescheduled'
  meeting_url?: string
  location?: string
  source: string
  cta_location?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  raw_data?: any
  created_at: string
  updated_at: string
}

// Database Operations
export const db = {
  // Contact operations
  contacts: {
    async create(contact: Omit<Contact, 'id' | 'created_at'>) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('contacts')
        .insert([contact])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getAll() {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },

    async getById(id: string) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    },
  },

  // Analytics operations
  analytics: {
    async track(analytics: Omit<Analytics, 'id' | 'created_at'>) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('analytics')
        .insert([analytics])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getPageViews(page?: string) {
      if (!supabase) throw new Error('Supabase not configured')

      let query = supabase
        .from('analytics')
        .select('*')
        .order('created_at', { ascending: false })

      if (page) {
        query = query.eq('page', page)
      }

      const { data, error } = await query

      if (error) throw error
      return data
    },

    async getStats() {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('analytics')
        .select('page, created_at')
        .gte(
          'created_at',
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        )

      if (error) throw error

      // Process stats
      const pageViews = data.length
      const uniquePages = new Set(data.map(item => item.page)).size
      const dailyViews = data.reduce(
        (acc, item) => {
          const date = new Date(item.created_at).toDateString()
          acc[date] = (acc[date] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      return {
        totalViews: pageViews,
        uniquePages,
        dailyViews,
      }
    },
  },

  // Booking operations
  bookings: {
    async create(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('bookings')
        .insert([{ ...booking, updated_at: new Date().toISOString() }])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getAll() {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },

    async getById(id: string) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    },

    async updateStatus(id: string, status: Booking['status'], notes?: string) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('bookings')
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getByEmail(email: string) {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('invitee_email', email)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },

    async getStats() {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase.rpc('get_booking_stats')
      if (error) throw error
      return data[0]
    },

    async getCTAPerformance() {
      if (!supabase) throw new Error('Supabase not configured')

      const { data, error } = await supabase.rpc('get_cta_performance')
      if (error) throw error
      return data
    },
  },
}

// Real-time subscriptions
export const subscriptions = {
  contacts: (callback: (payload: any) => void) => {
    if (!supabase) throw new Error('Supabase not configured')

    return supabase
      .channel('contacts')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'contacts' },
        callback
      )
      .subscribe()
  },

  bookings: (callback: (payload: any) => void) => {
    if (!supabase) throw new Error('Supabase not configured')

    return supabase
      .channel('bookings')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'bookings' },
        callback
      )
      .subscribe()
  },

  analytics: (callback: (payload: any) => void) => {
    if (!supabase) throw new Error('Supabase not configured')

    return supabase
      .channel('analytics')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'analytics' },
        callback
      )
      .subscribe()
  },
}
