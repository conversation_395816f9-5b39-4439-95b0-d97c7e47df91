# Supabase Configuration (Staging/Development)
NEXT_PUBLIC_SUPABASE_URL=https://hkbhttezrkjbyoougifv.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key_from_supabase_dashboard
SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key_from_supabase_dashboard

# Environment
NODE_ENV=development

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# Contact Form (Optional - for future email integration)
RESEND_API_KEY=your_resend_api_key
CONTACT_EMAIL=<EMAIL>

# Calendly Integration
NEXT_PUBLIC_CALENDLY_URL=https://calendly.com/denis-aidev/30min

# Vercel (for CI/CD)
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id
