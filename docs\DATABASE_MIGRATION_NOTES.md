# Database Migration - Add Notes Column

## Migration Required

To support the enhanced booking flow with project details, you need to add a `notes` column to the `bookings` table in Supabase.

## SQL Migration

Run this SQL in your Supabase SQL Editor:

```sql
-- Add notes column to bookings table
ALTER TABLE public.bookings
ADD COLUMN notes TEXT DEFAULT '';

-- Add comment for documentation
COMMENT ON COLUMN public.bookings.notes IS 'Project details and notes from the personal info form';

-- Update existing records to have empty notes (optional)
UPDATE public.bookings
SET notes = ''
WHERE notes IS NULL;
```

## Updated Schema

The `bookings` table now includes:

```sql
CREATE TABLE public.bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  calendly_id TEXT UNIQUE NOT NULL,
  calendly_event_uri TEXT DEFAULT '',
  calendly_invitee_uri TEXT DEFAULT '',
  invitee_id TEXT DEFAULT '',
  event_type TEXT DEFAULT 'Consultation',
  event_name TEXT DEFAULT 'Booking Call',
  invitee_email TEXT NOT NULL,
  invitee_name TEXT DEFAULT 'Unknown Client',
  invitee_timezone TEXT DEFAULT 'UTC',
  scheduled_at TIMESTAMPTZ DEFAULT NOW(),
  start_time TIMESTAMPTZ DEFAULT NOW(),
  end_time TIMESTAMPTZ DEFAULT NOW(),
  status TEXT CHECK (status IN ('active', 'canceled', 'completed', 'rescheduled')) DEFAULT 'active',
  meeting_url TEXT DEFAULT '',
  location TEXT DEFAULT 'Online',
  source TEXT DEFAULT 'website_popup',
  cta_location TEXT DEFAULT 'unknown',
  utm_source TEXT DEFAULT 'portfolio',
  utm_medium TEXT DEFAULT 'popup',
  utm_campaign TEXT DEFAULT 'booking',
  notes TEXT DEFAULT '', -- NEW COLUMN
  raw_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Features Enabled

With this migration, the booking system now:

1. **Captures Project Details**: Notes from the personal info form are saved to the database
2. **Pre-fills Calendly**: Project details appear in Calendly's "Please share anything that will help prepare for our meeting" field
3. **Enhanced Analytics**: Project details are available for analysis and preparation

## Verification

After running the migration, verify it worked:

```sql
-- Check the column was added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'bookings' AND column_name = 'notes';

-- Test inserting a record with notes
INSERT INTO public.bookings (
  calendly_id,
  invitee_email,
  invitee_name,
  notes
) VALUES (
  'test-' || gen_random_uuid()::text,
  '<EMAIL>',
  'Test User',
  'This is a test note from the personal info form'
);
```

## Rollback (if needed)

If you need to rollback this migration:

```sql
-- Remove the notes column
ALTER TABLE public.bookings DROP COLUMN notes;
```
