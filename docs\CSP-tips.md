## Update your next.config.js

/\*_ @type {import('next').NextConfig} _/
const nextConfig = {
async headers() {
return [
{
source: '/(.\*)',
headers: [
{
key: 'Content-Security-Policy',
value: [
// Basic sources
"default-src 'self'",

              // Script sources - Allow Calendly, Vercel, and Next.js
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://assets.calendly.com https://calendly.com https://vercel.live https://*.vercel.app https://*.vercel.com",

              // Style sources - Allow inline styles and Calendly
              "style-src 'self' 'unsafe-inline' https://assets.calendly.com https://fonts.googleapis.com",

              // Image sources - Allow all HTTPS images
              "img-src 'self' data: https: blob:",

              // Font sources
              "font-src 'self' https://fonts.gstatic.com https://assets.calendly.com",

              // Connect sources - Allow Supabase and Calendly API calls
              "connect-src 'self' https://api.calendly.com https://*.supabase.co https://*.supabase.io wss://*.supabase.co wss://*.supabase.io https://vercel.live wss://vercel.live",

              // Frame sources - Allow Calendly iframes
              "frame-src 'self' https://calendly.com https://*.calendly.com",

              // Worker and child sources for Next.js
              "worker-src 'self' blob:",
              "child-src 'self' blob:",

              // Object and media sources
              "object-src 'none'",
              "media-src 'self'",

              // Base URI
              "base-uri 'self'",

              // Form action
              "form-action 'self'",

              // Frame ancestors (for embedding)
              "frame-ancestors 'self'"
            ].join('; ')
          },
          // Additional security headers
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]

},

// Additional Next.js configuration
reactStrictMode: true,
swcMinify: true,

// Webpack configuration for better compatibility
webpack: (config, { isServer }) => {
if (!isServer) {
config.resolve.fallback = {
...config.resolve.fallback,
fs: false,
net: false,
tls: false,
}
}
return config
}
}

module.exports = nextConfig

## Updated CSP Configuration

// next.config.js
module.exports = {
async headers() {
return [
{
source: '/(._)',
headers: [
{
key: 'Content-Security-Policy',
value: `
default-src 'self';
script-src 'self' 'unsafe-inline' https://assets.calendly.com https://vercel.live;
style-src 'self' 'unsafe-inline' https://assets.calendly.com;
frame-src https://calendly.com;
connect-src 'self' https://calendly.com https://vercel.live https://_.supabase.co;
img-src 'self' data: https://assets.calendly.com;
font-src 'self' https://assets.calendly.com;
object-src 'none';
base-uri 'self';
form-action 'self';
`.replace(/\s{2,}/g, ' ').trim()
}
]
}
]
}
}

## Alternative: Meta Tag Approach

If you can't modify server headers, add this to your \_document.tsx:

// pages/\_document.tsx
import Document, { Html, Head, Main, NextScript } from 'next/document';

export default class MyDocument extends Document {
render() {
return (

<Html>
<Head>
<meta
httpEquiv="Content-Security-Policy"
content={`               default-src 'self';
              script-src 'self' 'unsafe-inline' https://assets.calendly.com https://vercel.live;
              style-src 'self' 'unsafe-inline' https://assets.calendly.com;
              frame-src https://calendly.com;
              connect-src 'self' https://calendly.com https://vercel.live https://*.supabase.co;
              img-src 'self' data: https://assets.calendly.com;
              font-src 'self' https://assets.calendly.com;
              object-src 'none';
            `.replace(/\s{2,}/g, ' ').trim()}
/>
</Head>
<body>
<Main />
<NextScript />
</body>
</Html>
);
}
}
