## let's create a simpler solution. Since the Calendly free plan only gives us URIs and not actual data, let's create a post-booking form that captures the details:

// components/PostBookingForm.tsx
'use client'

import { useState } from 'react'
import { ENV_CONFIG } from '@/lib/env'
import { createClient } from '@supabase/supabase-js'

interface PostBookingFormProps {
isOpen: boolean
onClose: () => void
eventUUID: string
eventUri: string
inviteeUri: string
}

export default function PostBookingForm({
isOpen,
onClose,
eventUUID,
eventUri,
inviteeUri
}: PostBookingFormProps) {
const [formData, setFormData] = useState({
name: '',
email: '',
phone: '',
company: '',
projectType: '',
notes: ''
})
const [isSubmitting, setIsSubmitting] = useState(false)
const [isSuccess, setIsSuccess] = useState(false)

const supabase = ENV_CONFIG.isSupabaseConfigured
? createClient(ENV_CONFIG.SUPABASE_URL, ENV_CONFIG.SUPABASE_ANON_KEY)
: null

const handleSubmit = async (e: React.FormEvent) => {
e.preventDefault()

    if (!supabase) {
      alert('Database not configured')
      return
    }

    setIsSubmitting(true)

    try {
      // Update the existing booking record with real data
      const { data, error } = await supabase
        .from('bookings')
        .update({
          invitee_name: formData.name,
          invitee_email: formData.email,
          event_name: `${formData.projectType || 'Consultation'} Call`,
          raw_data: {
            event_uuid: eventUUID,
            event_uri: eventUri,
            invitee_uri: inviteeUri,
            manual_data: formData,
            updated_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('calendly_id', eventUUID)
        .select()

      if (error) {
        console.error('Error updating booking:', error)

        // If update fails, create new record
        const bookingRecord = {
          calendly_id: eventUUID || `booking_${Date.now()}`,
          event_type: formData.projectType || 'Consultation',
          event_name: `${formData.projectType || 'Consultation'} Call`,
          invitee_email: formData.email,
          invitee_name: formData.name,
          invitee_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          scheduled_at: new Date().toISOString(),
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          status: 'active',
          meeting_url: '',
          location: 'Online',
          source: 'website_popup',
          utm_source: 'portfolio',
          utm_medium: 'popup',
          utm_campaign: 'booking',
          raw_data: {
            event_uuid: eventUUID,
            event_uri: eventUri,
            invitee_uri: inviteeUri,
            manual_data: formData,
            created_via: 'post_booking_form'
          }
        }

        const { error: insertError } = await supabase
          .from('bookings')
          .insert([bookingRecord])

        if (insertError) {
          throw insertError
        }
      }

      setIsSuccess(true)
      setTimeout(() => {
        onClose()
      }, 2000)

    } catch (error) {
      console.error('Error saving booking:', error)
      alert('Error saving your information. Please try again.')
    } finally {
      setIsSubmitting(false)
    }

}

const handleSkip = () => {
onClose()
}

if (!isOpen) return null

return (

<div className="post-booking-overlay">
<div className="post-booking-modal">
{isSuccess ? (
<div className="success-state">
<div className="success-icon">✅</div>
<h3>Information Saved!</h3>
<p>Thank you for providing your details. We'll be in touch soon!</p>
</div>
) : (
<div className="form-container">
<div className="form-header">
<h3>📝 Complete Your Booking</h3>
<p>Help us prepare for your call by sharing a few details:</p>
</div>

            <form onSubmit={handleSubmit} className="booking-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Full Name *</label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                    placeholder="Your full name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    placeholder="+****************"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="company">Company</label>
                  <input
                    type="text"
                    id="company"
                    value={formData.company}
                    onChange={(e) => setFormData({...formData, company: e.target.value})}
                    placeholder="Your company name"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="projectType">What can we help you with?</label>
                <select
                  id="projectType"
                  value={formData.projectType}
                  onChange={(e) => setFormData({...formData, projectType: e.target.value})}
                >
                  <option value="">Select a service...</option>
                  <option value="Web Development">Web Development</option>
                  <option value="Mobile App">Mobile App Development</option>
                  <option value="AI/ML Solutions">AI/ML Solutions</option>
                  <option value="Consulting">General Consulting</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="notes">Project Details</label>
                <textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows={4}
                  placeholder="Tell us about your project, goals, timeline, budget, etc."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={handleSkip} className="skip-btn">
                  Skip for now
                </button>
                <button type="submit" disabled={isSubmitting} className="submit-btn">
                  {isSubmitting ? 'Saving...' : 'Save & Continue'}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>

)
}

## Post-booking styles

/_ Post-Booking Form Styles - Add to globals.css _/

.post-booking-overlay {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(0, 0, 0, 0.8);
backdrop-filter: blur(8px);
display: flex;
align-items: center;
justify-content: center;
z-index: 10001;
padding: 20px;
animation: fadeIn 0.3s ease-out;
}

.post-booking-modal {
background: linear-gradient(135deg, #ffffff, #f8fafc);
border: 1px solid #e2e8f0;
border-radius: 16px;
box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
width: 100%;
max-width: 600px;
max-height: 90vh;
overflow-y: auto;
animation: slideUp 0.3s ease-out;
}

.form-container {
padding: 32px;
}

.form-header {
text-align: center;
margin-bottom: 32px;
}

.form-header h3 {
color: #1e293b;
font-size: 1.5rem;
font-weight: 600;
margin: 0 0 8px 0;
}

.form-header p {
color: #64748b;
font-size: 1rem;
margin: 0;
}

.booking-form {
display: flex;
flex-direction: column;
gap: 20px;
}

.form-row {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 16px;
}

.form-group {
display: flex;
flex-direction: column;
gap: 6px;
}

.form-group label {
color: #374151;
font-weight: 500;
font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
padding: 12px 16px;
border: 1px solid #d1d5db;
border-radius: 8px;
font-size: 1rem;
transition: all 0.2s ease;
background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
outline: none;
border-color: #06b6d4;
box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.form-group textarea {
resize: vertical;
min-height: 100px;
font-family: inherit;
}

.form-actions {
display: flex;
gap: 12px;
justify-content: flex-end;
margin-top: 24px;
padding-top: 24px;
border-top: 1px solid #e2e8f0;
}

.skip-btn {
padding: 12px 24px;
border: 1px solid #d1d5db;
background: white;
color: #64748b;
border-radius: 8px;
font-weight: 500;
cursor: pointer;
transition: all 0.2s ease;
}

.skip-btn:hover {
background: #f8fafc;
border-color: #94a3b8;
}

.submit-btn {
padding: 12px 24px;
border: none;
background: linear-gradient(135deg, #06b6d4, #0891b2);
color: white;
border-radius: 8px;
font-weight: 500;
cursor: pointer;
transition: all 0.2s ease;
min-width: 140px;
}

.submit-btn:hover:not(:disabled) {
background: linear-gradient(135deg, #0891b2, #0e7490);
transform: translateY(-1px);
box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.submit-btn:disabled {
opacity: 0.6;
cursor: not-allowed;
transform: none;
}

.success-state {
padding: 48px 32px;
text-align: center;
}

.success-icon {
font-size: 4rem;
margin-bottom: 16px;
}

.success-state h3 {
color: #10b981;
font-size: 1.5rem;
font-weight: 600;
margin: 0 0 12px 0;
}

.success-state p {
color: #64748b;
font-size: 1rem;
margin: 0;
}

/_ Mobile Responsiveness _/
@media (max-width: 768px) {
.post-booking-overlay {
padding: 16px;
}

.post-booking-modal {
max-height: 95vh;
}

.form-container {
padding: 24px;
}

.form-row {
grid-template-columns: 1fr;
gap: 12px;
}

.form-actions {
flex-direction: column;
}

.skip-btn,
.submit-btn {
width: 100%;
}
}

@media (max-width: 480px) {
.form-container {
padding: 20px;
}

.form-header h3 {
font-size: 1.25rem;
}

.success-state {
padding: 32px 20px;
}
}

## Now let's update your main Calendly modal to use the simpler post-booking form:

'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface CalendlyBookingModalProps {
isOpen: boolean
onClose: () => void
calendlyUrl?: string
}

// Supabase client setup with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey

const supabase = isSupabaseConfigured
? createClient(supabaseUrl, supabaseAnonKey)
: null

export default function CalendlyBookingModal({
isOpen,
onClose,
calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
const [isLoading, setIsLoading] = useState(true)
const [hasError, setHasError] = useState(false)
const [bookingSuccess, setBookingSuccess] = useState(false)
const [bookingData, setBookingData] = useState<any>(null)
const [postBookingOpen, setPostBookingOpen] = useState(false)
const [bookingInfo, setBookingInfo] = useState<any>(null)

// Save booking data to Supabase (Free Plan - Message Events Only)
const saveBookingToSupabase = async (eventData: any) => {
// Check if Supabase is configured
if (!ENV_CONFIG.isSupabaseConfigured) {
console.warn('⚠️ Database not configured, booking not saved:', 'Booking received but not saved to database')
console.log('📋 Booking data (would be saved if configured):', eventData)
return false
}

    if (!supabase) {
      console.error('❌ Supabase client not initialized')
      return false
    }

    try {
      // Extract data from Calendly message event
      const payload = eventData.payload || eventData

      console.log('🔍 Raw Calendly payload:', payload)

      // For free plan, we need to fetch the actual data from the URIs
      let bookingDetails = null
      let inviteeDetails = null

      // Try to fetch booking and invitee details from Calendly API
      try {
        if (payload.event?.uri) {
          console.log('📡 Fetching event details from:', payload.event.uri)
          const eventResponse = await fetch(payload.event.uri, {
            headers: {
              'Authorization': `Bearer ${ENV_CONFIG.SUPABASE_ANON_KEY}` // This won't work for Calendly API
            }
          })

          if (eventResponse.ok) {
            bookingDetails = await eventResponse.json()
            console.log('✅ Event details fetched:', bookingDetails)
          } else {
            console.log('⚠️ Could not fetch event details (expected for free plan)')
          }
        }

        if (payload.invitee?.uri) {
          console.log('📡 Fetching invitee details from:', payload.invitee.uri)
          const inviteeResponse = await fetch(payload.invitee.uri, {
            headers: {
              'Authorization': `Bearer ${ENV_CONFIG.SUPABASE_ANON_KEY}` // This won't work for Calendly API
            }
          })

          if (inviteeResponse.ok) {
            inviteeDetails = await inviteeResponse.json()
            console.log('✅ Invitee details fetched:', inviteeDetails)
          } else {
            console.log('⚠️ Could not fetch invitee details (expected for free plan)')
          }
        }
      } catch (apiError) {
        console.log('ℹ️ API fetch failed (expected for free plan):', apiError)
      }

      // Extract UUID from URIs for better tracking
      const extractUUID = (uri: string) => {
        const match = uri.match(/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i)
        return match ? match[1] : null
      }

      const eventUUID = payload.event?.uri ? extractUUID(payload.event.uri) : null
      const inviteeUUID = payload.invitee?.uri ? extractUUID(payload.invitee.uri) : null

      // Create booking record with available data
      const bookingRecord = {
        calendly_id: eventUUID || `booking_${Date.now()}`,
        event_type: bookingDetails?.resource?.event_type?.name || 'Consultation',
        event_name: bookingDetails?.resource?.name || 'Booking Call',
        invitee_email: inviteeDetails?.resource?.email || '<EMAIL>',
        invitee_name: inviteeDetails?.resource?.name || 'Pending Client',
        invitee_timezone: inviteeDetails?.resource?.timezone || 'UTC',
        scheduled_at: bookingDetails?.resource?.start_time || new Date().toISOString(),
        start_time: bookingDetails?.resource?.start_time || new Date().toISOString(),
        end_time: bookingDetails?.resource?.end_time || new Date().toISOString(),
        status: 'active',
        meeting_url: bookingDetails?.resource?.location?.join_url || '',
        location: bookingDetails?.resource?.location?.type || 'Online',
        source: 'website_popup',
        utm_source: 'portfolio',
        utm_medium: 'popup',
        utm_campaign: 'booking',
        raw_data: {
          ...eventData,
          event_uuid: eventUUID,
          invitee_uuid: inviteeUUID,
          event_uri: payload.event?.uri,
          invitee_uri: payload.invitee?.uri,
          fetched_details: {
            event: bookingDetails,
            invitee: inviteeDetails
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('💾 Attempting to save booking to Supabase:', bookingRecord)

      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingRecord])
        .select()

      if (error) {
        console.error('❌ Error saving booking to Supabase:', error)
        console.error('📋 Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        return false
      }

      console.log('✅ Booking saved successfully to Supabase:', data)
      setBookingData(data[0])

      // Show a note about pending data
      console.log('ℹ️ Note: Full booking details will be available in your Calendly dashboard')

      return true
    } catch (error) {
      console.error('❌ Error in saveBookingToSupabase:', error)
      return false
    }

}

// Handle Calendly message events (Free Plan Method)
useEffect(() => {
if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Security: Only accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      console.log('📅 Calendly Event:', event, payload)

      switch (event) {
        case 'calendly.profile_page_viewed':
          console.log('👀 Profile page viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.event_type_viewed':
          console.log('📋 Event type viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('🕐 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!', payload)
          setBookingSuccess(true)

          // Extract UUIDs from URIs
          const extractUUID = (uri: string) => {
            const match = uri.match(/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i)
            return match ? match[1] : null
          }

          const eventUUID = payload.event?.uri ? extractUUID(payload.event.uri) : null
          const inviteeUUID = payload.invitee?.uri ? extractUUID(payload.invitee.uri) : null

          // Store booking info for form
          setBookingInfo({
            eventUUID,
            eventUri: payload.event?.uri,
            inviteeUri: payload.invitee?.uri
          })

          // Save initial booking record with UUIDs
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            console.log('✅ Initial booking data saved to database')
          }

          // Show post-booking form after 3 seconds
          setTimeout(() => {
            setPostBookingOpen(true)
          }, 3000)

          // Auto-close main modal after 8 seconds
          setTimeout(() => {
            if (!postBookingOpen) {
              handleClose()
            }
          }, 8000)
          break

        case 'calendly.page_height':
          // Handle iframe height changes
          console.log('📏 Page height changed:', payload)
          break

        default:
          console.log('ℹ️ Other Calendly event:', event, payload)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)

}, [isOpen])

// Handle iframe load events
const handleIframeLoad = useCallback(() => {
console.log('🔄 Calendly iframe loaded')
// Give iframe time to initialize
setTimeout(() => {
if (isLoading) {
setIsLoading(false)
}
}, 2000)
}, [isLoading])

// Handle iframe error
const handleIframeError = useCallback(() => {
console.error('❌ Calendly iframe failed to load')
setHasError(true)
setIsLoading(false)
}, [])

// Handle modal close
const handleClose = useCallback(() => {
setBookingSuccess(false)
setBookingData(null)
setBookingInfo(null)
setPostBookingOpen(false)
setIsLoading(true)
setHasError(false)
onClose()
}, [onClose])

// Handle escape key
useEffect(() => {
const handleEscape = (e: KeyboardEvent) => {
if (e.key === 'Escape' && isOpen) {
handleClose()
}
}

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }

}, [isOpen, handleClose])

// Don't render if not open
if (!isOpen) return null

return (
<>

<div className="calendly-modal-overlay" onClick={handleClose}>
<div
className="calendly-modal-content"
onClick={(e) => e.stopPropagation()} >
{/_ Modal Header _/}
<div className="calendly-modal-header">
<h2>Schedule Your Call</h2>
<button
              className="calendly-close-btn"
              onClick={handleClose}
              aria-label="Close booking modal"
            >
<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
<path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
</svg>
</button>
</div>

          {/* Modal Body */}
          <div className="calendly-modal-body">
            {/* Success State */}
            {bookingSuccess && (
              <div className="calendly-success">
                <div className="success-icon">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                    <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                </div>
                <h3>Booking Confirmed! 🎉</h3>
                <p>Your call has been scheduled successfully.</p>
                <p>You'll receive a confirmation email from Calendly shortly.</p>
                <p className="success-note">Please provide your details in the next step...</p>
              </div>
            )}

            {/* Loading State */}
            {isLoading && !bookingSuccess && (
              <div className="calendly-loading">
                <div className="loading-spinner">
                  <div className="spinner"></div>
                </div>
                <p>Loading your booking calendar...</p>
              </div>
            )}

            {/* Error State */}
            {hasError && !bookingSuccess && (
              <div className="calendly-error">
                <div className="error-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                    <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                    <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                </div>
                <h3>Unable to load calendar</h3>
                <p>We're having trouble loading the booking calendar. Please try the direct link below.</p>
                <a
                  href={calendlyUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="calendly-fallback-btn"
                >
                  Open Calendly in New Tab
                </a>
              </div>
            )}

            {/* Calendly Iframe */}
            {!isLoading && !hasError && !bookingSuccess && (
              <div className="calendly-iframe-container">
                <iframe
                  src={`${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`}
                  width="100%"
                  height="600"
                  frameBorder="0"
                  title="Schedule a meeting"
                  onLoad={handleIframeLoad}
                  onError={handleIframeError}
                  style={{
                    border: 'none',
                    borderRadius: '0 0 20px 20px'
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Post-Booking Form */}
      {postBookingOpen && bookingInfo && (
        <PostBookingForm
          isOpen={postBookingOpen}
          onClose={() => {
            setPostBookingOpen(false)
            handleClose()
          }}
          eventUUID={bookingInfo.eventUUID}
          eventUri={bookingInfo.eventUri}
          inviteeUri={bookingInfo.inviteeUri}
        />
      )}
    </>

)\_banner=1`}
width="100%"
height="600"
frameBorder="0"
title="Schedule a meeting"
onLoad={handleIframeLoad}
onError={handleIframeError}
style={{
                  border: 'none',
                  borderRadius: '0 0 20px 20px'
                }}
/>

</div>
)}
</div>
</div>
</div>
)
}
