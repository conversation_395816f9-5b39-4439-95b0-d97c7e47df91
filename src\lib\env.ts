// Environment configuration with validation and debugging

export const ENV_CONFIG = {
  // Supabase Configuration
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',

  // Calendly Configuration
  CALENDLY_URL: process.env.NEXT_PUBLIC_CALENDLY_URL || '',

  // Environment Info
  NODE_ENV: process.env.NODE_ENV || 'development',
  VERCEL_ENV: process.env.VERCEL_ENV || 'development',

  // Validation flags
  get isSupabaseConfigured() {
    return Boolean(this.SUPABASE_URL && this.SUPABASE_ANON_KEY)
  },

  get isCalendlyConfigured() {
    return Boolean(this.CALENDLY_URL)
  },

  get isProduction() {
    return this.NODE_ENV === 'production' || this.VERCEL_ENV === 'production'
  },
}

// Debug function to log environment status
export function debugEnvironment() {
  const debug = {
    timestamp: new Date().toISOString(),
    environment: ENV_CONFIG.NODE_ENV,
    vercelEnv: ENV_CONFIG.VERCEL_ENV,
    supabase: {
      url: ENV_CONFIG.SUPABASE_URL
        ? `✅ Set (${ENV_CONFIG.SUPABASE_URL.substring(0, 30)}...)`
        : '❌ Not set',
      anonKey: ENV_CONFIG.SUPABASE_ANON_KEY
        ? `✅ Set (${ENV_CONFIG.SUPABASE_ANON_KEY.length} chars)`
        : '❌ Not set',
      serviceKey: ENV_CONFIG.SUPABASE_SERVICE_KEY
        ? `✅ Set (${ENV_CONFIG.SUPABASE_SERVICE_KEY.length} chars)`
        : '❌ Not set',
      configured: ENV_CONFIG.isSupabaseConfigured ? '✅ Yes' : '❌ No',
    },
    calendly: {
      url: ENV_CONFIG.CALENDLY_URL
        ? `✅ Set (${ENV_CONFIG.CALENDLY_URL})`
        : '❌ Not set',
      configured: ENV_CONFIG.isCalendlyConfigured ? '✅ Yes' : '❌ No',
    },
    clientSide: typeof window !== 'undefined' ? 'Yes' : 'No',
  }

  console.log('🔍 Environment Debug:', debug)
  return debug
}

// Validation function
export function validateEnvironment() {
  const errors = []

  if (!ENV_CONFIG.SUPABASE_URL) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is not set')
  }

  if (!ENV_CONFIG.SUPABASE_ANON_KEY) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is not set')
  }

  if (!ENV_CONFIG.CALENDLY_URL) {
    errors.push('NEXT_PUBLIC_CALENDLY_URL is not set')
  }

  if (errors.length > 0) {
    console.error('❌ Environment validation failed:', errors)
    return { valid: false, errors }
  }

  console.log('✅ Environment validation passed')
  return { valid: true, errors: [] }
}

// Export individual values for convenience
export const {
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_KEY,
  CALENDLY_URL,
  NODE_ENV,
  VERCEL_ENV,
} = ENV_CONFIG
