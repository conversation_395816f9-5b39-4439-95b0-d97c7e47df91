'use client'

import { useState } from 'react'
import { ENV_CONFIG } from '@/lib/env'
import { createClient } from '@supabase/supabase-js'

interface PostBookingFormProps {
  isOpen: boolean
  onClose: () => void
  eventUUID: string
  eventUri: string
  inviteeUri: string
}

export default function PostBookingForm({
  isOpen,
  onClose,
  eventUUID,
  eventUri,
  inviteeUri,
}: PostBookingFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    projectType: '',
    notes: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  const supabase = ENV_CONFIG.isSupabaseConfigured
    ? createClient(ENV_CONFIG.SUPABASE_URL, ENV_CONFIG.SUPABASE_ANON_KEY)
    : null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!supabase) {
      alert('Database not configured')
      return
    }

    setIsSubmitting(true)

    try {
      // Update the existing booking record with real data
      const { data, error } = await supabase
        .from('bookings')
        .update({
          invitee_name: formData.name,
          invitee_email: formData.email,
          event_name: `${formData.projectType || 'Consultation'} Call`,
          raw_data: {
            event_uuid: eventUUID,
            event_uri: eventUri,
            invitee_uri: inviteeUri,
            manual_data: formData,
            updated_at: new Date().toISOString(),
          },
          updated_at: new Date().toISOString(),
        })
        .eq('calendly_id', eventUUID)
        .select()

      if (error) {
        console.error('Error updating booking:', error)

        // If update fails, create new record
        const bookingRecord = {
          calendly_id: eventUUID || `booking_${Date.now()}`,
          calendly_event_uri: eventUri,
          calendly_invitee_uri: inviteeUri,
          event_type: formData.projectType || 'Consultation',
          event_name: `${formData.projectType || 'Consultation'} Call`,
          invitee_email: formData.email,
          invitee_name: formData.name,
          invitee_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          scheduled_at: new Date().toISOString(),
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          status: 'active',
          meeting_url: '',
          location: 'Online',
          source: 'website_popup',
          cta_location: 'post_booking_form',
          utm_source: 'portfolio',
          utm_medium: 'popup',
          utm_campaign: 'booking',
          raw_data: {
            event_uuid: eventUUID,
            event_uri: eventUri,
            invitee_uri: inviteeUri,
            manual_data: formData,
            created_via: 'post_booking_form',
          },
        }

        const { error: insertError } = await supabase
          .from('bookings')
          .insert([bookingRecord])

        if (insertError) {
          throw insertError
        }
      }

      console.log('✅ Booking updated with real user data:', formData)
      setIsSuccess(true)
      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.error('Error saving booking:', error)
      alert('Error saving your information. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSkip = () => {
    console.log('ℹ️ User skipped post-booking form')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="post-booking-overlay">
      <div className="post-booking-modal">
        {isSuccess ? (
          <div className="success-state">
            <div className="success-icon">✅</div>
            <h3>Information Saved!</h3>
            <p>Thank you for providing your details. We'll be in touch soon!</p>
          </div>
        ) : (
          <div className="form-container">
            <div className="form-header">
              <h3>📝 Complete Your Booking</h3>
              <p>Help us prepare for your call by sharing a few details:</p>
            </div>

            <form onSubmit={handleSubmit} className="booking-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Full Name *</label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={e =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    required
                    placeholder="Your full name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={e =>
                      setFormData({ ...formData, email: e.target.value })
                    }
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    value={formData.phone}
                    onChange={e =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    placeholder="+****************"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="company">Company</label>
                  <input
                    type="text"
                    id="company"
                    value={formData.company}
                    onChange={e =>
                      setFormData({ ...formData, company: e.target.value })
                    }
                    placeholder="Your company name"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="projectType">What can we help you with?</label>
                <select
                  id="projectType"
                  value={formData.projectType}
                  onChange={e =>
                    setFormData({ ...formData, projectType: e.target.value })
                  }
                >
                  <option value="">Select a service...</option>
                  <option value="AI Automation">AI Automation</option>
                  <option value="Web Development">Web Development</option>
                  <option value="Mobile App">Mobile App Development</option>
                  <option value="AI/ML Solutions">AI/ML Solutions</option>
                  <option value="Consulting">General Consulting</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="notes">Project Details</label>
                <textarea
                  id="notes"
                  value={formData.notes}
                  onChange={e =>
                    setFormData({ ...formData, notes: e.target.value })
                  }
                  rows={4}
                  placeholder="Tell us about your project, goals, timeline, budget, etc."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={handleSkip} className="skip-btn">
                  Skip for now
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="submit-btn"
                >
                  {isSubmitting ? 'Saving...' : 'Save & Continue'}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}
