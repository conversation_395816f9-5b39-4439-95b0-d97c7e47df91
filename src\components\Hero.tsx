import Link from 'next/link'
import AutomationAnimation from './AutomationAnimation'
import CalendlyButton from './CalendlyButton'

export default function Hero() {
  return (
    <section id="hero" className="section">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1>
              Transform Your Business Into A{' '}
              <span className="highlight">Revenue Machine</span> With{' '}
              <span className="highlight">AI-Powered Automations</span>
            </h1>
            <p className="subheadline">
              I help businesses achieve extraordinary growth by implementing
              strategic AI automation systems that work 24/7 to drive results.
            </p>

            <div className="hero-stats">
              <span>AI Automation Specialist</span>
              <span>Process Optimization Expert</span>
              <span>1 Year Experience</span>
            </div>

            <div className="hero-cta">
              <Link href="#case-study" className="btn btn-primary">
                View Case Studies
              </Link>
              <CalendlyButton
                className="btn btn-secondary"
                ctaLocation="hero"
                variant="button"
              >
                Book a Call
              </CalendlyButton>
            </div>
          </div>

          <div className="hero-image">
            <AutomationAnimation />
          </div>
        </div>
      </div>
    </section>
  )
}
