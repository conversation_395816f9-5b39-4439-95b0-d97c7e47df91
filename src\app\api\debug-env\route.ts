import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Check all environment variables with detailed info
    const envCheck = {
      supabaseUrl: {
        value: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
        length: process.env.NEXT_PUBLIC_SUPABASE_URL?.length || 0,
        preview:
          process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...' ||
          'undefined',
        isPlaceholder:
          process.env.NEXT_PUBLIC_SUPABASE_URL ===
          'https://placeholder.supabase.co',
      },
      anonKey: {
        value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
        length: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
        preview:
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...' ||
          'undefined',
        isPlaceholder:
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY === 'placeholder-key',
      },
      serviceKey: {
        value: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
        length: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
        preview:
          process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20) + '...' ||
          'undefined',
        isPlaceholder:
          process.env.SUPABASE_SERVICE_ROLE_KEY === 'placeholder-key',
      },
      calendlyUrl: {
        value: process.env.NEXT_PUBLIC_CALENDLY_URL ? 'Set' : 'Not set',
        length: process.env.NEXT_PUBLIC_CALENDLY_URL?.length || 0,
        preview: process.env.NEXT_PUBLIC_CALENDLY_URL || 'undefined',
      },
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
      vercelUrl: process.env.VERCEL_URL,
      allSupabaseKeys: Object.keys(process.env)
        .filter(key => key.includes('SUPABASE'))
        .sort(),
      allCalendlyKeys: Object.keys(process.env)
        .filter(key => key.includes('CALENDLY'))
        .sort(),
      totalEnvVars: Object.keys(process.env).length,
    }

    // Test Supabase connection if variables are available
    let supabaseTest = 'Not tested'
    try {
      if (
        process.env.NEXT_PUBLIC_SUPABASE_URL &&
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
        process.env.NEXT_PUBLIC_SUPABASE_URL !==
          'https://placeholder.supabase.co'
      ) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
          {
            headers: {
              apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            },
          }
        )
        supabaseTest = response.ok
          ? `Connection successful (${response.status})`
          : `Connection failed (${response.status}): ${response.statusText}`
      } else {
        supabaseTest = 'Skipped - missing or placeholder variables'
      }
    } catch (error) {
      supabaseTest = `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }

    // Test bookings table access
    let bookingsTest = 'Not tested'
    try {
      if (
        process.env.NEXT_PUBLIC_SUPABASE_URL &&
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
        process.env.NEXT_PUBLIC_SUPABASE_URL !==
          'https://placeholder.supabase.co'
      ) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/bookings?select=count&limit=1`,
          {
            headers: {
              apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
              Authorization: `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            },
          }
        )
        bookingsTest = response.ok
          ? `Bookings table accessible (${response.status})`
          : `Bookings table error (${response.status}): ${response.statusText}`
      }
    } catch (error) {
      bookingsTest = `Bookings test error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }

    return NextResponse.json({
      message: 'Environment Variables Debug Report - v2',
      timestamp: new Date().toISOString(),
      environment: envCheck,
      tests: {
        supabaseConnection: supabaseTest,
        bookingsTableAccess: bookingsTest,
      },
      recommendations: {
        hasValidSupabaseConfig:
          envCheck.supabaseUrl.value === 'Set' &&
          envCheck.anonKey.value === 'Set' &&
          !envCheck.supabaseUrl.isPlaceholder &&
          !envCheck.anonKey.isPlaceholder,
        hasServiceKey:
          envCheck.serviceKey.value === 'Set' &&
          !envCheck.serviceKey.isPlaceholder,
        readyForProduction:
          envCheck.supabaseUrl.value === 'Set' &&
          envCheck.anonKey.value === 'Set' &&
          envCheck.serviceKey.value === 'Set' &&
          !envCheck.supabaseUrl.isPlaceholder &&
          !envCheck.anonKey.isPlaceholder &&
          !envCheck.serviceKey.isPlaceholder,
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        message: 'Debug endpoint error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
