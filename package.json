{"name": "denis-erastus-portfolio", "version": "1.0.0", "description": "AI Automation Expert Portfolio - <PERSON>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo \"No tests specified\" && exit 0", "test:watch": "echo \"No tests specified\" && exit 0", "test:coverage": "echo \"No tests specified\" && exit 0", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "jest": "^29.0.0", "@testing-library/react": "^13.0.0", "@testing-library/jest-dom": "^6.0.0", "jest-environment-jsdom": "^29.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/ProDevDenis/MyPortfolio.git"}, "author": "<PERSON>", "license": "MIT", "keywords": ["portfolio", "ai-automation", "next.js", "react", "typescript", "supabase"]}