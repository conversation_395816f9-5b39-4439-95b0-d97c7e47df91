import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Environment variable validation with detailed logging
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Enhanced validation
const hasValidConfig =
  supabaseUrl &&
  supabaseServiceKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseServiceKey !== 'placeholder-key' &&
  supabaseUrl.includes('supabase.co')

// Log configuration status (server-side only)
console.log('🔧 Bookings API Configuration:')
console.log('  Supabase URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
console.log('  Service Key:', supabaseServiceKey ? '✅ Set' : '❌ Missing')
console.log('  Anon Key:', supabaseAnonKey ? '✅ Set' : '❌ Missing')
console.log('  Valid Config:', hasValidConfig)
console.log('  Environment:', process.env.NODE_ENV)
console.log('  Vercel Env:', process.env.VERCEL_ENV)

// Create admin client for server-side operations
let supabaseAdmin: any = null
if (hasValidConfig) {
  try {
    supabaseAdmin = createClient(supabaseUrl!, supabaseServiceKey!, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })
    console.log('✅ Supabase admin client created successfully')
  } catch (error) {
    console.error('❌ Failed to create Supabase admin client:', error)
  }
} else {
  console.log('⚠️ Supabase admin client not created - invalid configuration')
}

// Create regular client as fallback
let supabaseClient: any = null
if (
  supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'placeholder-key'
) {
  try {
    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })
    console.log('✅ Supabase client created successfully')
  } catch (error) {
    console.error('❌ Failed to create Supabase client:', error)
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('📥 Booking API POST request received')

    // Try admin client first (preferred for security)
    if (hasValidConfig && supabaseAdmin) {
      console.log('🔐 Using admin client for booking save')
      return await saveBookingWithAdmin(request, supabaseAdmin)
    }

    // Fallback to regular client if admin not available
    if (supabaseClient) {
      console.log('🔄 Falling back to regular client for booking save')
      return await saveBookingWithClient(request, supabaseClient)
    }

    // No Supabase configuration available
    console.log('⚠️ No Supabase configuration available')
    return NextResponse.json(
      {
        success: false,
        error: 'Database not configured',
        message: 'Booking received but not saved to database',
        debug: {
          hasValidConfig,
          hasAdmin: !!supabaseAdmin,
          hasClient: !!supabaseClient,
          url: supabaseUrl ? 'Set' : 'Missing',
          serviceKey: supabaseServiceKey ? 'Set' : 'Missing',
          anonKey: supabaseAnonKey ? 'Set' : 'Missing',
        },
      },
      { status: 200 } // Return 200 so the frontend doesn't show an error
    )
  } catch (error) {
    console.error('❌ Booking API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// Save booking using admin client (preferred)
async function saveBookingWithAdmin(request: NextRequest, adminClient: any) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.calendly_id || !body.invitee_email) {
      return NextResponse.json(
        { error: 'Missing required fields: calendly_id, invitee_email' },
        { status: 400 }
      )
    }

    // Enhanced booking record with Calendly URI data
    const bookingRecord = {
      calendly_id: body.calendly_id,
      calendly_event_uri: body.calendly_event_uri || '',
      calendly_invitee_uri: body.calendly_invitee_uri || '',
      invitee_id: body.invitee_id || '',
      event_type: body.event_type || 'Consultation',
      event_name: body.event_name || 'Booking Call',
      invitee_email: body.invitee_email,
      invitee_name: body.invitee_name || 'Unknown Client',
      invitee_timezone: body.invitee_timezone || 'UTC',
      scheduled_at: body.scheduled_at || new Date().toISOString(),
      start_time: body.start_time || new Date().toISOString(),
      end_time: body.end_time || new Date().toISOString(),
      status: body.status || 'active',
      meeting_url: body.meeting_url || '',
      location: body.location || 'Online',
      source: body.source || 'website_popup',
      cta_location: body.cta_location || 'unknown',
      utm_source: body.utm_source || 'portfolio',
      utm_medium: body.utm_medium || 'popup',
      utm_campaign: body.utm_campaign || 'booking',
      notes: body.notes || '',
      raw_data: body.raw_data || body,
      updated_at: new Date().toISOString(),
    }

    console.log('💾 Enhanced booking record with Calendly data:', {
      calendly_id: bookingRecord.calendly_id,
      calendly_event_uri: bookingRecord.calendly_event_uri,
      calendly_invitee_uri: bookingRecord.calendly_invitee_uri,
      invitee_id: bookingRecord.invitee_id,
      event_type: bookingRecord.event_type,
      invitee_email: bookingRecord.invitee_email,
      invitee_name: bookingRecord.invitee_name,
    })

    // Insert into Supabase using service role
    const { data, error } = await supabaseAdmin
      .from('bookings')
      .insert([bookingRecord])
      .select()
      .single()

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { error: 'Failed to save booking', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Booking saved successfully:', data.id)
    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// Save booking using regular client (fallback)
async function saveBookingWithClient(request: NextRequest, client: any) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.calendly_id || !body.invitee_email) {
      return NextResponse.json(
        { error: 'Missing required fields: calendly_id, invitee_email' },
        { status: 400 }
      )
    }

    // Enhanced booking record (same as admin)
    const bookingRecord = {
      calendly_id: body.calendly_id,
      calendly_event_uri: body.calendly_event_uri || '',
      calendly_invitee_uri: body.calendly_invitee_uri || '',
      invitee_id: body.invitee_id || '',
      event_type: body.event_type || 'Consultation',
      event_name: body.event_name || 'Booking Call',
      invitee_email: body.invitee_email,
      invitee_name: body.invitee_name || 'Unknown Client',
      invitee_timezone: body.invitee_timezone || 'UTC',
      scheduled_at: body.scheduled_at || new Date().toISOString(),
      start_time: body.start_time || new Date().toISOString(),
      end_time: body.end_time || new Date().toISOString(),
      status: body.status || 'active',
      meeting_url: body.meeting_url || '',
      location: body.location || 'Online',
      source: body.source || 'website_popup',
      cta_location: body.cta_location || 'unknown',
      utm_source: body.utm_source || 'portfolio',
      utm_medium: body.utm_medium || 'popup',
      utm_campaign: body.utm_campaign || 'booking',
      notes: body.notes || '',
      raw_data: body.raw_data || body,
      updated_at: new Date().toISOString(),
    }

    console.log(
      '💾 Saving booking with regular client:',
      bookingRecord.calendly_id
    )

    // Insert using regular client (anon key with RLS)
    const { data, error } = await client
      .from('bookings')
      .insert([bookingRecord])
      .select()
      .single()

    if (error) {
      console.error('❌ Regular client error:', error)
      return NextResponse.json(
        { error: 'Failed to save booking', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Booking saved successfully with regular client:', data.id)
    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('❌ Client save error:', error)
    return NextResponse.json(
      {
        error: 'Client save failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bookings API endpoint',
    configured: hasValidConfig,
    supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
    serviceKey: supabaseServiceKey ? 'Set' : 'Not set',
    // Debug info
    envUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'undefined',
    envKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'exists' : 'missing',
    timestamp: new Date().toISOString(),
  })
}
