## typescript

'use client'

import { useEffect, useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

interface CalendlyBookingModalProps {
isOpen: boolean
onClose: () => void
calendlyUrl?: string
}

// Supabase client setup
const supabase = createClient(
process.env.NEXT_PUBLIC_SUPABASE_URL!,
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export default function CalendlyBookingModal({
isOpen,
onClose,
calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
const [isLoading, setIsLoading] = useState(true)
const [hasError, setHasError] = useState(false)
const [bookingSuccess, setBookingSuccess] = useState(false)
const [bookingData, setBookingData] = useState<any>(null)

// Save booking data to Supabase (Free Plan - Message Events Only)
const saveBookingToSupabase = async (eventData: any) => {
try {
// Extract data from Calendly message event
const payload = eventData.payload || eventData

      // Create booking record with available data
      const bookingRecord = {
        calendly_id: payload.event?.uuid || `booking_${Date.now()}`,
        event_type: payload.event?.event_type?.name || 'Consultation',
        event_name: payload.event?.name || 'Booking Call',
        invitee_email: payload.invitee?.email || '<EMAIL>',
        invitee_name: payload.invitee?.name || 'Unknown Client',
        invitee_timezone: payload.invitee?.timezone || 'UTC',
        scheduled_at: payload.event?.start_time || new Date().toISOString(),
        start_time: payload.event?.start_time || new Date().toISOString(),
        end_time: payload.event?.end_time || new Date().toISOString(),
        status: 'active',
        meeting_url: payload.event?.location?.join_url || '',
        location: payload.event?.location?.type || 'Online',
        source: 'website_popup',
        utm_source: 'portfolio',
        utm_medium: 'popup',
        utm_campaign: 'booking',
        raw_data: eventData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('💾 Saving booking to Supabase:', bookingRecord)

      const { data, error } = await supabase
        .from('bookings')
        .insert([bookingRecord])
        .select()

      if (error) {
        console.error('❌ Error saving booking:', error)
        return false
      }

      console.log('✅ Booking saved successfully:', data)
      setBookingData(data[0])
      return true
    } catch (error) {
      console.error('❌ Error in saveBookingToSupabase:', error)
      return false
    }

}

// Handle Calendly message events (Free Plan Method)
useEffect(() => {
if (!isOpen) return

    const handleCalendlyMessage = async (e: MessageEvent) => {
      // Security: Only accept messages from Calendly
      if (e.origin !== 'https://calendly.com') return

      const { event, payload } = e.data

      console.log('📅 Calendly Event:', event, payload)

      switch (event) {
        case 'calendly.profile_page_viewed':
          console.log('👀 Profile page viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.event_type_viewed':
          console.log('📋 Event type viewed')
          setIsLoading(false)
          setHasError(false)
          break

        case 'calendly.date_and_time_selected':
          console.log('🕐 Date and time selected')
          break

        case 'calendly.event_scheduled':
          console.log('🎉 Event scheduled!', payload)
          setBookingSuccess(true)

          // Save to Supabase
          const saved = await saveBookingToSupabase(e.data)
          if (saved) {
            console.log('✅ Booking data saved to database')
          }

          // Auto-close modal after 3 seconds
          setTimeout(() => {
            handleClose()
          }, 3000)
          break

        case 'calendly.page_height':
          // Handle iframe height changes
          console.log('📏 Page height changed:', payload)
          break

        default:
          console.log('ℹ️ Other Calendly event:', event, payload)
      }
    }

    window.addEventListener('message', handleCalendlyMessage)
    return () => window.removeEventListener('message', handleCalendlyMessage)

}, [isOpen])

// Handle iframe load events
const handleIframeLoad = useCallback(() => {
console.log('🔄 Calendly iframe loaded')
// Give iframe time to initialize
setTimeout(() => {
if (isLoading) {
setIsLoading(false)
}
}, 2000)
}, [isLoading])

// Handle iframe error
const handleIframeError = useCallback(() => {
console.error('❌ Calendly iframe failed to load')
setHasError(true)
setIsLoading(false)
}, [])

// Handle modal close
const handleClose = useCallback(() => {
setBookingSuccess(false)
setBookingData(null)
setIsLoading(true)
setHasError(false)
onClose()
}, [onClose])

// Handle escape key
useEffect(() => {
const handleEscape = (e: KeyboardEvent) => {
if (e.key === 'Escape' && isOpen) {
handleClose()
}
}

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }

}, [isOpen, handleClose])

// Don't render if not open
if (!isOpen) return null

return (

<div className="calendly-modal-overlay" onClick={handleClose}>
<div
className="calendly-modal-content"
onClick={(e) => e.stopPropagation()} >
{/_ Modal Header _/}
<div className="calendly-modal-header">
<h2>Schedule Your Call</h2>
<button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
<path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
</svg>
</button>
</div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {/* Success State */}
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Booking Confirmed! 🎉</h3>
              <p>Your call has been scheduled successfully.</p>
              <p>You'll receive a confirmation email from Calendly shortly.</p>
              {bookingData && (
                <div className="booking-details">
                  <p><strong>Event:</strong> {bookingData.event_name}</p>
                  <p><strong>Date:</strong> {new Date(bookingData.start_time).toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {new Date(bookingData.start_time).toLocaleTimeString()}</p>
                </div>
              )}
              <p className="success-note">This window will close automatically...</p>
            </div>
          )}

          {/* Loading State */}
          {isLoading && !bookingSuccess && (
            <div className="calendly-loading">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <p>Loading your booking calendar...</p>
            </div>
          )}

          {/* Error State */}
          {hasError && !bookingSuccess && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open Calendly in New Tab
              </a>
            </div>
          )}

          {/* Calendly Iframe */}
          {!isLoading && !hasError && !bookingSuccess && (
            <div className="calendly-iframe-container">
              <iframe
                src={`${calendlyUrl}?embed_domain=${typeof window !== 'undefined' ? window.location.hostname : 'localhost'}&embed_type=Inline&hide_gdpr_banner=1`}
                width="100%"
                height="600"
                frameBorder="0"
                title="Schedule a meeting"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{
                  border: 'none',
                  borderRadius: '0 0 20px 20px'
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>

)
}

## Supabase

- Create bookings table
  CREATE TABLE IF NOT EXISTS public.bookings (
  id BIGSERIAL PRIMARY KEY,
  -- Basic Calendly Data (from message events)
  calendly_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL DEFAULT 'Consultation',
  event_name TEXT NOT NULL DEFAULT 'Booking Call',
  -- Client Information
  invitee_email TEXT NOT NULL,
  invitee_name TEXT NOT NULL,
  invitee_timezone TEXT DEFAULT 'UTC',
  -- Scheduling Information
  scheduled_at TIMESTAMPTZ NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'completed')),
  -- Meeting Details
  meeting_url TEXT,
  location TEXT DEFAULT 'Online',
  -- Tracking Information
  source TEXT DEFAULT 'website_popup',
  utm_source TEXT DEFAULT 'portfolio',
  utm_medium TEXT DEFAULT 'popup',
  utm_campaign TEXT DEFAULT 'booking',
  -- Raw Data Storage
  raw_data JSONB,
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_calendly_id ON public.bookings(calendly_id);
CREATE INDEX IF NOT EXISTS idx_bookings_email ON public.bookings(invitee_email);
CREATE INDEX IF NOT EXISTS idx_bookings_scheduled_at ON public.bookings(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON public.bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_bookings_source ON public.bookings(source);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
NEW.updated_at = NOW();
RETURN NEW;
END;

$$
language 'plpgsql';

CREATE TRIGGER update_bookings_updated_at
  BEFORE UPDATE ON public.bookings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================
-- ROW LEVEL SECURITY (RLS) - Simple Setup
-- ============================================

-- Enable RLS
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Policy for anonymous users (allow insert for new bookings)
CREATE POLICY "Anyone can create bookings" ON public.bookings
  FOR INSERT WITH CHECK (true);

-- Policy for authenticated users (read their own bookings)
CREATE POLICY "Users can read all bookings" ON public.bookings
  FOR SELECT USING (true);

-- ============================================
-- SIMPLE ANALYTICS FUNCTIONS
-- ============================================

-- Get basic booking statistics
CREATE OR REPLACE FUNCTION public.get_booking_stats()
RETURNS TABLE(
  total_bookings BIGINT,
  active_bookings BIGINT,
  completed_bookings BIGINT,
  today_bookings BIGINT,
  this_week_bookings BIGINT,
  this_month_bookings BIGINT
) AS
$$

BEGIN
RETURN QUERY
SELECT
COUNT(_) as total_bookings,
COUNT(_) FILTER (WHERE status = 'active') as active*bookings,
COUNT(*) FILTER (WHERE status = 'completed') as completed*bookings,
COUNT(*) FILTER (WHERE DATE(created*at) = CURRENT_DATE) as today_bookings,
COUNT(*) FILTER (WHERE created*at >= DATE_TRUNC('week', NOW())) as this_week_bookings,
COUNT(*) FILTER (WHERE created_at >= DATE_TRUNC('month', NOW())) as this_month_bookings
FROM public.bookings;
END;

$$
LANGUAGE plpgsql;

-- Get upcoming bookings
CREATE OR REPLACE FUNCTION public.get_upcoming_bookings(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  status TEXT,
  days_until INTEGER
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.end_time,
b.status,
EXTRACT(DAY FROM (b.start_time - NOW()))::INTEGER as days_until
FROM public.bookings b
WHERE b.start_time > NOW()
AND b.status = 'active'
ORDER BY b.start_time ASC
LIMIT limit_count;
END;

$$
LANGUAGE plpgsql;

-- Get recent bookings
CREATE OR REPLACE FUNCTION public.get_recent_bookings(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  status TEXT,
  created_at TIMESTAMPTZ
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.status,
b.created_at
FROM public.bookings b
ORDER BY b.created_at DESC
LIMIT limit_count;
END;

$$
LANGUAGE plpgsql;

-- ============================================
-- SIMPLE ANALYTICS VIEW
-- ============================================

-- Daily booking summary
CREATE OR REPLACE VIEW public.daily_booking_summary AS
SELECT
  DATE(created_at) as booking_date,
  COUNT(*) as total_bookings,
  COUNT(*) FILTER (WHERE status = 'active') as active_bookings,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
  COUNT(DISTINCT invitee_email) as unique_clients,
  event_type,
  source
FROM public.bookings
GROUP BY DATE(created_at), event_type, source
ORDER BY booking_date DESC;

-- Monthly booking trends
CREATE OR REPLACE VIEW public.monthly_booking_trends AS
SELECT
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_bookings,
  COUNT(DISTINCT invitee_email) as unique_clients,
  event_type,
  source,
  ROUND(AVG(EXTRACT(EPOCH FROM (end_time - start_time))/60), 2) as avg_duration_minutes
FROM public.bookings
GROUP BY DATE_TRUNC('month', created_at), event_type, source
ORDER BY month DESC;

-- ============================================
-- SAMPLE DATA (Optional - for testing)
-- ============================================

-- Insert sample booking for testing
INSERT INTO public.bookings (
  calendly_id,
  event_type,
  event_name,
  invitee_email,
  invitee_name,
  invitee_timezone,
  scheduled_at,
  start_time,
  end_time,
  status,
  meeting_url,
  location,
  source,
  utm_source,
  utm_medium,
  utm_campaign
) VALUES (
  'sample_free_plan_booking',
  'Consultation',
  'Initial Consultation Call',
  '<EMAIL>',
  'Test Client',
  'America/New_York',
  NOW() + INTERVAL '1 day',
  NOW() + INTERVAL '1 day',
  NOW() + INTERVAL '1 day 30 minutes',
  'active',
  'https://calendly.com/events/sample/meeting',
  'Google Meet',
  'website_popup',
  'portfolio',
  'popup',
  'booking'
) ON CONFLICT (calendly_id) DO NOTHING;

-- ============================================
-- UTILITY FUNCTIONS FOR FREE PLAN
-- ============================================

-- Manual booking status update (since no webhooks)
CREATE OR REPLACE FUNCTION public.update_booking_status(
  booking_calendly_id TEXT,
  new_status TEXT
)
RETURNS BOOLEAN AS
$$

BEGIN
UPDATE public.bookings
SET
status = new_status,
updated_at = NOW()
WHERE calendly_id = booking_calendly_id;

RETURN FOUND;
END;

$$
LANGUAGE plpgsql;

-- Get booking by Calendly ID
CREATE OR REPLACE FUNCTION public.get_booking_by_calendly_id(booking_calendly_id TEXT)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  status TEXT,
  location TEXT,
  created_at TIMESTAMPTZ
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.end_time,
b.status,
b.location,
b.created_at
FROM public.bookings b
WHERE b.calendly_id = booking_calendly_id;
END;

$$
LANGUAGE plpgsql;

-- ============================================
-- TESTING QUERIES
-- ============================================

-- Test the setup
SELECT 'Bookings table created successfully' as status;

-- Test sample data
SELECT COUNT(*) as sample_bookings FROM public.bookings;

-- Test analytics function
SELECT * FROM public.get_booking_stats();

-- Test recent bookings
SELECT * FROM public.get_recent_bookings(5);

## Modal
- Create bookings table
CREATE TABLE IF NOT EXISTS public.bookings (
  id BIGSERIAL PRIMARY KEY,

  -- Basic Calendly Data (from message events)
  calendly_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL DEFAULT 'Consultation',
  event_name TEXT NOT NULL DEFAULT 'Booking Call',

  -- Client Information
  invitee_email TEXT NOT NULL,
  invitee_name TEXT NOT NULL,
  invitee_timezone TEXT DEFAULT 'UTC',

  -- Scheduling Information
  scheduled_at TIMESTAMPTZ NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'completed')),

  -- Meeting Details
  meeting_url TEXT,
  location TEXT DEFAULT 'Online',

  -- Tracking Information
  source TEXT DEFAULT 'website_popup',
  utm_source TEXT DEFAULT 'portfolio',
  utm_medium TEXT DEFAULT 'popup',
  utm_campaign TEXT DEFAULT 'booking',

  -- Raw Data Storage
  raw_data JSONB,

  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_calendly_id ON public.bookings(calendly_id);
CREATE INDEX IF NOT EXISTS idx_bookings_email ON public.bookings(invitee_email);
CREATE INDEX IF NOT EXISTS idx_bookings_scheduled_at ON public.bookings(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON public.bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_bookings_source ON public.bookings(source);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS
$$

BEGIN
NEW.updated_at = NOW();
RETURN NEW;
END;

$$
language 'plpgsql';

CREATE TRIGGER update_bookings_updated_at
  BEFORE UPDATE ON public.bookings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================
-- ROW LEVEL SECURITY (RLS) - Simple Setup
-- ============================================

-- Enable RLS
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Policy for anonymous users (allow insert for new bookings)
CREATE POLICY "Anyone can create bookings" ON public.bookings
  FOR INSERT WITH CHECK (true);

-- Policy for authenticated users (read their own bookings)
CREATE POLICY "Users can read all bookings" ON public.bookings
  FOR SELECT USING (true);

-- ============================================
-- SIMPLE ANALYTICS FUNCTIONS
-- ============================================

-- Get basic booking statistics
CREATE OR REPLACE FUNCTION public.get_booking_stats()
RETURNS TABLE(
  total_bookings BIGINT,
  active_bookings BIGINT,
  completed_bookings BIGINT,
  today_bookings BIGINT,
  this_week_bookings BIGINT,
  this_month_bookings BIGINT
) AS
$$

BEGIN
RETURN QUERY
SELECT
COUNT(_) as total_bookings,
COUNT(_) FILTER (WHERE status = 'active') as active*bookings,
COUNT(*) FILTER (WHERE status = 'completed') as completed*bookings,
COUNT(*) FILTER (WHERE DATE(created*at) = CURRENT_DATE) as today_bookings,
COUNT(*) FILTER (WHERE created*at >= DATE_TRUNC('week', NOW())) as this_week_bookings,
COUNT(*) FILTER (WHERE created_at >= DATE_TRUNC('month', NOW())) as this_month_bookings
FROM public.bookings;
END;

$$
LANGUAGE plpgsql;

-- Get upcoming bookings
CREATE OR REPLACE FUNCTION public.get_upcoming_bookings(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  status TEXT,
  days_until INTEGER
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.end_time,
b.status,
EXTRACT(DAY FROM (b.start_time - NOW()))::INTEGER as days_until
FROM public.bookings b
WHERE b.start_time > NOW()
AND b.status = 'active'
ORDER BY b.start_time ASC
LIMIT limit_count;
END;

$$
LANGUAGE plpgsql;

-- Get recent bookings
CREATE OR REPLACE FUNCTION public.get_recent_bookings(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  status TEXT,
  created_at TIMESTAMPTZ
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.status,
b.created_at
FROM public.bookings b
ORDER BY b.created_at DESC
LIMIT limit_count;
END;

$$
LANGUAGE plpgsql;

-- ============================================
-- SIMPLE ANALYTICS VIEW
-- ============================================

-- Daily booking summary
CREATE OR REPLACE VIEW public.daily_booking_summary AS
SELECT
  DATE(created_at) as booking_date,
  COUNT(*) as total_bookings,
  COUNT(*) FILTER (WHERE status = 'active') as active_bookings,
  COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
  COUNT(DISTINCT invitee_email) as unique_clients,
  event_type,
  source
FROM public.bookings
GROUP BY DATE(created_at), event_type, source
ORDER BY booking_date DESC;

-- Monthly booking trends
CREATE OR REPLACE VIEW public.monthly_booking_trends AS
SELECT
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_bookings,
  COUNT(DISTINCT invitee_email) as unique_clients,
  event_type,
  source,
  ROUND(AVG(EXTRACT(EPOCH FROM (end_time - start_time))/60), 2) as avg_duration_minutes
FROM public.bookings
GROUP BY DATE_TRUNC('month', created_at), event_type, source
ORDER BY month DESC;

-- ============================================
-- SAMPLE DATA (Optional - for testing)
-- ============================================

-- Insert sample booking for testing
INSERT INTO public.bookings (
  calendly_id,
  event_type,
  event_name,
  invitee_email,
  invitee_name,
  invitee_timezone,
  scheduled_at,
  start_time,
  end_time,
  status,
  meeting_url,
  location,
  source,
  utm_source,
  utm_medium,
  utm_campaign
) VALUES (
  'sample_free_plan_booking',
  'Consultation',
  'Initial Consultation Call',
  '<EMAIL>',
  'Test Client',
  'America/New_York',
  NOW() + INTERVAL '1 day',
  NOW() + INTERVAL '1 day',
  NOW() + INTERVAL '1 day 30 minutes',
  'active',
  'https://calendly.com/events/sample/meeting',
  'Google Meet',
  'website_popup',
  'portfolio',
  'popup',
  'booking'
) ON CONFLICT (calendly_id) DO NOTHING;

-- ============================================
-- UTILITY FUNCTIONS FOR FREE PLAN
-- ============================================

-- Manual booking status update (since no webhooks)
CREATE OR REPLACE FUNCTION public.update_booking_status(
  booking_calendly_id TEXT,
  new_status TEXT
)
RETURNS BOOLEAN AS
$$

BEGIN
UPDATE public.bookings
SET
status = new_status,
updated_at = NOW()
WHERE calendly_id = booking_calendly_id;

RETURN FOUND;
END;

$$
LANGUAGE plpgsql;

-- Get booking by Calendly ID
CREATE OR REPLACE FUNCTION public.get_booking_by_calendly_id(booking_calendly_id TEXT)
RETURNS TABLE(
  id BIGINT,
  calendly_id TEXT,
  event_name TEXT,
  invitee_name TEXT,
  invitee_email TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  status TEXT,
  location TEXT,
  created_at TIMESTAMPTZ
) AS
$$

BEGIN
RETURN QUERY
SELECT
b.id,
b.calendly_id,
b.event_name,
b.invitee_name,
b.invitee_email,
b.start_time,
b.end_time,
b.status,
b.location,
b.created_at
FROM public.bookings b
WHERE b.calendly_id = booking_calendly_id;
END;

$$
LANGUAGE plpgsql;

-- ============================================
-- TESTING QUERIES
-- ============================================

-- Test the setup
SELECT 'Bookings table created successfully' as status;

-- Test sample data
SELECT COUNT(*) as sample_bookings FROM public.bookings;

-- Test analytics function
SELECT * FROM public.get_booking_stats();

-- Test recent bookings
SELECT * FROM public.get_recent_bookings(5);

## Securing
Content Security Policy
Update your next.config.js:
javascript/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' https://assets.calendly.com https://calendly.com",
              "style-src 'self' 'unsafe-inline' https://assets.calendly.com",
              "img-src 'self' data: https:",
              "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
              "frame-src 'self' https://calendly.com https://*.calendly.com",
            ].join('; ')
          }
        ]
      }
    ]
  }
}

module.exports = nextConfig
$$
