'use client'

import { useState } from 'react'
import { trackEvent } from '@/lib/analytics'

interface PersonalInfoFormProps {
  isOpen: boolean
  onClose: () => void
  onNext: (formData: PersonalInfoData) => void
  ctaLocation: string
}

export interface PersonalInfoData {
  name: string
  email: string
  notes: string
  ctaLocation: string
}

export default function PersonalInfoForm({
  isOpen,
  onClose,
  onNext,
  ctaLocation,
}: PersonalInfoFormProps) {
  const [formData, setFormData] = useState<PersonalInfoData>({
    name: '',
    email: '',
    notes: '',
    ctaLocation,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.email) {
      alert('Please fill in your name and email address.')
      return
    }

    setIsSubmitting(true)

    try {
      // Track form submission
      await trackEvent('personal_info_form_submitted', {
        cta_location: ctaLocation,
        has_notes: <PERSON><PERSON><PERSON>(formData.notes),
      })

      console.log('📝 Personal info collected:', {
        name: formData.name,
        email: formData.email,
        ctaLocation,
      })

      // Pass data to next step
      onNext(formData)
    } catch (error) {
      console.error('Error submitting personal info:', error)
      alert('Error submitting form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    trackEvent('personal_info_form_closed', { cta_location: ctaLocation })
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="personal-info-overlay">
      <div className="personal-info-modal">
        <div className="form-header">
          <div className="step-indicator">
            <div className="step active">
              <span className="step-number">1</span>
              <span className="step-label">Your Details</span>
            </div>
            <div className="step-divider"></div>
            <div className="step">
              <span className="step-number">2</span>
              <span className="step-label">Select Time</span>
            </div>
          </div>

          <button
            onClick={handleClose}
            className="close-btn"
            aria-label="Close"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        <div className="form-content">
          <div className="form-intro">
            <h3>📞 Let's Talk.</h3>
            <p>Every Game Changer Starts with a Conversation</p>
          </div>

          <form onSubmit={handleSubmit} className="personal-info-form">
            <div className="form-group">
              <label htmlFor="name">
                Full Name <span className="required">*</span>
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={e =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
                placeholder="Your full name"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">
                Email Address <span className="required">*</span>
              </label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={e =>
                  setFormData({ ...formData, email: e.target.value })
                }
                required
                placeholder="<EMAIL>"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="notes">Project Details (Optional)</label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={e =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                rows={3}
                placeholder="Brief description..."
                className="form-textarea"
              />
            </div>

            <div className="form-actions">
              <button
                type="button"
                onClick={handleClose}
                className="cancel-btn"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !formData.name || !formData.email}
                className="next-btn"
              >
                {isSubmitting ? (
                  <>
                    <div className="spinner"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    Next: Select Time
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M5 12H19M19 12L12 5M19 12L12 19"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
