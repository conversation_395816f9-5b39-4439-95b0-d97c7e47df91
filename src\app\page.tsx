'use client'

// Portfolio main page - AI Automation Expert
import { useEffect } from 'react'
import dynamic from 'next/dynamic'
import Header from '@/components/Header'
import Hero from '@/components/Hero'
import Insights from '@/components/Insights'
import Story from '@/components/Story'
import CaseStudy from '@/components/CaseStudy'
import Testimonials from '@/components/Testimonials'
import FAQ from '@/components/FAQ'
import Footer from '@/components/Footer'
import FloatingContactButton from '@/components/FloatingContactButton'
import { trackPageView } from '@/lib/analytics'

// Dynamic import for ContactModal to avoid build issues
const ContactModal = dynamic(() => import('@/components/ContactModal'), {
  ssr: false,
})

export default function Home() {
  useEffect(() => {
    // Track page view
    trackPageView('/')
  }, [])

  return (
    <>
      <Header />
      <FloatingContactButton />
      <ContactModal />

      <main>
        <Hero />
        <Insights />
        <Story />
        <CaseStudy />
        <Testimonials />
        <FAQ />
      </main>

      <Footer />
    </>
  )
}
